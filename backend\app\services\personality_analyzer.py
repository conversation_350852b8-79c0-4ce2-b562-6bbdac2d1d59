"""
Advanced personality analysis service for 100% personality cloning
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
import instructor
import google.generativeai as genai
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from dotenv import load_dotenv

from app.llm.schemas import (
    DeepAnalysisResult, QuestioningStrategy, EmpatheticQuestion,
    PersonalitySnapshot, SituationalResponse, PersonalitySimilarity,
    BehaviorPattern, ValueSystem, CognitiveStyle
)
from app.llm.prompts import (
    PERSONALITY_ANALYSIS_PROMPT, STRATEGIC_QUESTIONING_PROMPT,
    EMPATHETIC_QUESTION_PROMPT, PERSONALITY_PREDICTION_PROMPT,
    SIMILARITY_ASSESSMENT_PROMPT, BEHAVIOR_PATTERN_ANALYSIS_PROMPT,
    VALUE_SYSTEM_ANALYSIS_PROMPT, COGNITIVE_STYLE_ANALYSIS_PROMPT
)
from app.database.models import PersonalityProfile, Message, Conversation

load_dotenv()

class PersonalityAnalyzer:
    def __init__(self):
        # Initialize Gemini client
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        self.model = genai.GenerativeModel(model_name="gemini-1.5-pro")

        # Fallback to OpenAI if available
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "YOUR_OPENAI_API_KEY_HERE":
            import openai
            self.openai_client = instructor.patch(openai.OpenAI(api_key=openai_key))
        else:
            self.openai_client = None
        
    async def safe_llm_call(self, prompt: str, response_model, max_retries: int = 3):
        """Safe LLM call with retry mechanism and fallback"""
        for attempt in range(max_retries):
            try:
                response = await self.client.generate_content(
                    prompt,
                    response_model=response_model
                )
                return response
            except Exception as e:
                print(f"LLM call attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    return self._get_fallback_response(response_model)
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
    
    def _get_fallback_response(self, response_model):
        """Generate fallback response when LLM fails"""
        if response_model == DeepAnalysisResult:
            return DeepAnalysisResult(
                psychological_summary="分析暂时不可用，请稍后重试。",
                personality_updates=PersonalitySnapshot(
                    target_name="Unknown",
                    analysis_timestamp="",
                    completion_percentage=0.0,
                    big_five=[],
                    cognitive_style=CognitiveStyle(
                        primary_style="unknown",
                        decision_speed=0.5,
                        risk_tolerance=0.5,
                        analytical_depth=0.5
                    ),
                    emotional_profile={},
                    communication_style={},
                    personality_insights=[],
                    behavior_patterns=[],
                    value_system=ValueSystem(
                        core_values=[],
                        value_hierarchy=[],
                        moral_framework="",
                        ethical_boundaries=[],
                        value_conflicts=[]
                    ),
                    decision_patterns=[],
                    stress_responses=[],
                    growth_areas=[]
                ),
                new_insights=[],
                confidence_score=0.0,
                postgres_operations=[],
                neo4j_operations=[],
                chromadb_operations=[]
            )
        elif response_model == EmpatheticQuestion:
            return EmpatheticQuestion(
                question_text="请告诉我更多关于这个人的信息。",
                question_context="通用询问",
                expected_response_type="描述性"
            )
        # Add more fallback responses as needed
        return None

    async def analyze_personality_deep(
        self, 
        user_narrative: str, 
        conversation_history: List[str],
        target_name: str,
        current_personality_state: Dict[str, Any],
        db: AsyncSession
    ) -> DeepAnalysisResult:
        """Perform deep personality analysis"""
        
        prompt = PERSONALITY_ANALYSIS_PROMPT.format(
            target_name=target_name,
            user_narrative=user_narrative,
            conversation_history="\n".join(conversation_history),
            current_personality_state=json.dumps(current_personality_state, ensure_ascii=False, indent=2)
        )
        
        result = await self.safe_llm_call(prompt, DeepAnalysisResult)
        return result

    async def generate_strategic_questions(
        self,
        target_name: str,
        completion_percentage: float,
        collected_insights: Dict[str, Any],
        current_focus_areas: List[str],
        missing_information: List[str],
        conversation_history: List[str]
    ) -> QuestioningStrategy:
        """Generate strategic questioning plan"""
        
        prompt = STRATEGIC_QUESTIONING_PROMPT.format(
            target_name=target_name,
            completion_percentage=completion_percentage,
            collected_insights=json.dumps(collected_insights, ensure_ascii=False, indent=2),
            current_focus_areas=", ".join(current_focus_areas),
            missing_information=", ".join(missing_information),
            conversation_history="\n".join(conversation_history)
        )
        
        result = await self.safe_llm_call(prompt, QuestioningStrategy)
        return result

    async def generate_empathetic_question(
        self,
        strategic_goal: str,
        target_insight: str,
        conversation_history: List[str]
    ) -> EmpatheticQuestion:
        """Generate empathetic question based on strategy"""
        
        prompt = EMPATHETIC_QUESTION_PROMPT.format(
            strategic_goal=strategic_goal,
            target_insight=target_insight,
            conversation_history="\n".join(conversation_history)
        )
        
        result = await self.safe_llm_call(prompt, EmpatheticQuestion)
        return result

    async def predict_personality_response(
        self,
        target_name: str,
        personality_profile: Dict[str, Any],
        situation_description: str
    ) -> SituationalResponse:
        """Predict how the target person would respond in a given situation"""
        
        prompt = PERSONALITY_PREDICTION_PROMPT.format(
            target_name=target_name,
            personality_profile=json.dumps(personality_profile, ensure_ascii=False, indent=2),
            situation_description=situation_description
        )
        
        result = await self.safe_llm_call(prompt, SituationalResponse)
        return result

    async def assess_personality_similarity(
        self,
        target_name: str,
        constructed_personality: Dict[str, Any],
        reference_personality: Optional[Dict[str, Any]] = None
    ) -> PersonalitySimilarity:
        """Assess similarity between constructed and reference personality"""
        
        prompt = SIMILARITY_ASSESSMENT_PROMPT.format(
            target_name=target_name,
            constructed_personality=json.dumps(constructed_personality, ensure_ascii=False, indent=2),
            reference_personality=json.dumps(reference_personality or {}, ensure_ascii=False, indent=2)
        )
        
        result = await self.safe_llm_call(prompt, PersonalitySimilarity)
        return result

    async def analyze_behavior_patterns(
        self,
        target_name: str,
        user_narrative: str
    ) -> List[BehaviorPattern]:
        """Analyze behavior patterns from narrative"""
        
        prompt = BEHAVIOR_PATTERN_ANALYSIS_PROMPT.format(
            target_name=target_name,
            user_narrative=user_narrative
        )
        
        result = await self.safe_llm_call(prompt, BehaviorPattern)
        return [result] if result else []

    async def analyze_value_system(
        self,
        target_name: str,
        relevant_narratives: List[str]
    ) -> ValueSystem:
        """Analyze value system and moral framework"""
        
        prompt = VALUE_SYSTEM_ANALYSIS_PROMPT.format(
            target_name=target_name,
            relevant_narratives="\n".join(relevant_narratives)
        )
        
        result = await self.safe_llm_call(prompt, ValueSystem)
        return result

    async def analyze_cognitive_style(
        self,
        target_name: str,
        cognitive_information: str
    ) -> CognitiveStyle:
        """Analyze cognitive style and thinking patterns"""
        
        prompt = COGNITIVE_STYLE_ANALYSIS_PROMPT.format(
            target_name=target_name,
            cognitive_information=cognitive_information
        )
        
        result = await self.safe_llm_call(prompt, CognitiveStyle)
        return result

    async def get_personality_completion_status(
        self,
        personality_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Calculate personality analysis completion status"""
        
        # Query database for current personality data
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            return {"completion_percentage": 0.0, "missing_areas": []}
        
        # Calculate completion based on available data
        completion_factors = {
            "basic_info": 1.0 if personality.target_name else 0.0,
            "big_five": sum([
                1.0 if personality.openness_score != 0.5 else 0.0,
                1.0 if personality.conscientiousness_score != 0.5 else 0.0,
                1.0 if personality.extraversion_score != 0.5 else 0.0,
                1.0 if personality.agreeableness_score != 0.5 else 0.0,
                1.0 if personality.neuroticism_score != 0.5 else 0.0,
            ]) / 5.0,
            "cognitive_style": 1.0 if personality.dominant_cognitive_style else 0.0,
            "communication": 1.0 if personality.average_response_length else 0.0,
        }
        
        overall_completion = sum(completion_factors.values()) / len(completion_factors) * 100
        
        missing_areas = [
            area for area, score in completion_factors.items() if score < 0.8
        ]
        
        return {
            "completion_percentage": overall_completion,
            "missing_areas": missing_areas,
            "completion_factors": completion_factors
        }
